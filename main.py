"""
FastAPI应用主入口
"""

from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from api.auth_routes import auth_router
from api.routes import router
from api.report_routes import router as report_router
from api.word_format_routes import router as word_format_router
from api.ai_stream_routes import router as ai_stream_router
from config import settings
from fastapi.openapi.utils import get_openapi
from core.exceptions import Text2SQLException
from core.logging import get_logger, setup_logging
from core.request_context import RequestContextMiddleware
from core.security_headers import SecurityHeadersMiddleware

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("AI REPORT服务启动中...")
    
    try:
        # 初始化数据库
        from database.connection import db_manager
        # try:
        #     init_database()
        #     logger.info("数据库初始化成功")
        # except Exception as e:
        #     logger.warning("数据库初始化失败", error=str(e))

        # 测试数据库连接
        if db_manager.test_connection():
            logger.info("数据库连接测试成功")
        else:
            logger.warning("数据库连接测试失败")

        logger.info("AI REPORT服务启动完成")
        
    except Exception as e:
        logger.error("服务启动失败", error=str(e))
        raise
    
    yield
    
    # 关闭时执行
    logger.info("AI REPORT服务关闭中...")
    logger.info("AI REPORT服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="report ai backend",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    debug=settings.debug
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# 请求上下文与安全头
app.add_middleware(RequestContextMiddleware)
app.add_middleware(SecurityHeadersMiddleware)


# 全局异常处理器
@app.exception_handler(Text2SQLException)
async def text2sql_exception_handler(request, exc: Text2SQLException):
    """Text2SQL异常处理器"""
    logger.error(
        "Text2SQL异常",
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details
    )

    from models.schemas import ErrorResponse

    error_response = ErrorResponse(
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details
    )

    return JSONResponse(
        status_code=400,
        content=error_response.model_dump()
    )


@app.exception_handler(Exception)
async def global_exception_handler(request, exc: Exception):
    """全局异常处理器"""
    logger.error("未处理的异常", error=str(exc), exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error_code": "INTERNAL_ERROR",
            "message": "内部服务错误",
            "details": {"error": str(exc)} if settings.debug else {}
        }
    )


# 注册路由
app.include_router(router, prefix=settings.api_prefix)
app.include_router(auth_router, prefix=settings.api_prefix)
app.include_router(report_router)
app.include_router(word_format_router)
app.include_router(ai_stream_router)  # AI streaming routes


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI REPORT API",
        "version": settings.version,
        "docs": "/docs",
        "health": f"{settings.api_prefix}/health"
    }


# 健康检查（简化版）
@app.get("/ping")
async def ping():
    """简单的健康检查"""
    return {"status": "ok", "message": "pong"}


def main(query_optimzation: str, GenSQL: str, ExeSQL: str, answer: str) -> str:
    """
    按顺序拼接四个参数内容：
    - query_optimzation 每行加 markdown 引用 >
    - GenSQL 原样
    - ExeSQL 原样，后多加一个换行
    - answer 原样
    参数为空则输出空内容。
    """
    def quote_block(text: str) -> str:
        if not text:
            return ''
        return '\n'.join(f'> {line}' if line.strip() else '>' for line in text.splitlines())

    quoted_query = quote_block(query_optimzation)
    gen_sql = GenSQL if GenSQL else ''
    ex_sql = (ExeSQL + '\n') if ExeSQL else ''
    ans = answer if answer else ''
    return '\n'.join([quoted_query, gen_sql, ex_sql, ans])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host=settings.host, port=settings.port, reload=settings.debug)

# 自定义 OpenAPI，注入 Bearer 安全方案并设置全局安全要求
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title=settings.app_name,
        version=settings.version,
        description="report ai backend",
        routes=app.routes,
    )
    security_schemes = {
        "bearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }
    openapi_schema.setdefault("components", {}).setdefault("securitySchemes", {}).update(security_schemes)
    # 全局安全要求（标注文档；实际访问控制以依赖为准）
    openapi_schema["security"] = [{"bearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
