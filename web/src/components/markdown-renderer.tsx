"use client"

import { useState, useEffect } from 'react'
import DOMPurify from 'dompurify'
import { Chart<PERSON><PERSON>er, TableRenderer } from './chart-renderer'
import { apiClient } from '@/lib/api-client'

interface Chart {
  chartId?: string
  id?: string
  title?: string
  type: string
  data?: any
  datasets?: any[]
}

interface Table {
  id: string
  title?: string
  headers: string[]
  rows: string[][]
}

interface MarkdownRendererProps {
  markdown: string
  charts: Chart[]
  tables: Table[]
}

export function MarkdownRenderer({ markdown, charts, tables }: MarkdownRendererProps) {
  const [processedContent, setProcessedContent] = useState<string>(markdown)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    const processMarkdown = async () => {
      setIsProcessing(true)
      let processedMarkdown = markdown

      try {
        // 处理图表占位符 [CHART:chart_id] 
        const chartPlaceholders = processedMarkdown.match(/\[CHART:[^\]]+\]/g) || []
        
        for (const placeholder of chartPlaceholders) {
          const chartId = placeholder.match(/\[CHART:([^\]]+)\]/)?.[1]
          if (chartId) {
            // 根据ID查找对应的图表配置
            const chart = charts.find(c => c.chartId === chartId || c.id === chartId)
            if (chart) {
              try {
                // 生成SVG
                const svgContent = await apiClient.generateChartSVG(chart)
                // 安全过滤SVG内容
                const sanitizedSVG = DOMPurify.sanitize(svgContent, {
                  ALLOWED_TAGS: ['svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'defs', 'pattern'],
                  ALLOWED_ATTR: ['width', 'height', 'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'cx', 'cy', 'r', 'x', 'y', 'font-size', 'font-weight', 'text-anchor', 'class', 'id']
                })
                // 替换占位符为安全的SVG
                processedMarkdown = processedMarkdown.replace(
                  placeholder, 
                  `<div class="chart-container my-4">${sanitizedSVG}</div>`
                )
              } catch (error) {
                console.error('Failed to generate chart SVG:', error)
                // 替换为错误信息
                processedMarkdown = processedMarkdown.replace(
                  placeholder,
                  `<div class="chart-error p-4 border border-red-200 rounded-lg bg-red-50">
                    <p class="text-red-600">图表加载失败: ${DOMPurify.sanitize(chartId)}</p>
                  </div>`
                )
              }
            } else {
              // 找不到对应图表配置
              processedMarkdown = processedMarkdown.replace(
                placeholder,
                `<div class="chart-error p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                  <p class="text-yellow-600">未找到图表: ${DOMPurify.sanitize(chartId)}</p>
                </div>`
              )
            }
          }
        }

        // 处理表格占位符 [TABLE:table_id]
        const tablePlaceholders = processedMarkdown.match(/\[TABLE:[^\]]+\]/g) || []
        
        for (const placeholder of tablePlaceholders) {
          const tableId = placeholder.match(/\[TABLE:([^\]]+)\]/)?.[1]
          if (tableId) {
            // 根据ID查找对应的表格配置
            const table = tables.find(t => t.id === tableId)
            if (table) {
              // 生成HTML表格
              const tableHTML = generateTableHTML(table)
              // 替换占位符
              processedMarkdown = processedMarkdown.replace(placeholder, tableHTML)
            } else {
              // 找不到对应表格
              processedMarkdown = processedMarkdown.replace(
                placeholder,
                `<div class="table-error p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                  <p class="text-yellow-600">未找到表格: ${DOMPurify.sanitize(tableId)}</p>
                </div>`
              )
            }
          }
        }

        setProcessedContent(processedMarkdown)
      } catch (error) {
        console.error('Error processing markdown:', error)
        setProcessedContent(markdown)
      } finally {
        setIsProcessing(false)
      }
    }

    processMarkdown()
  }, [markdown, charts, tables])

  // 安全过滤最终的HTML内容
  const sanitizedContent = DOMPurify.sanitize(processedContent, {
    ALLOWED_TAGS: ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'table', 'thead', 'tbody', 'tr', 'th', 'td'],
    ALLOWED_ATTR: ['class', 'id', 'href', 'target', 'width', 'height', 'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'cx', 'cy', 'r', 'x', 'y', 'font-size', 'font-weight', 'text-anchor']
  })

  if (isProcessing) {
    return (
      <div className="prose prose-lg max-w-none dark:prose-invert">
        <div className="flex items-center justify-center p-4 text-gray-500">
          正在处理内容...
        </div>
      </div>
    )
  }

  return (
    <div className="prose prose-lg max-w-none dark:prose-invert">
      <div 
        dangerouslySetInnerHTML={{ __html: sanitizedContent }}
      />
    </div>
  )
}

// 生成表格HTML的辅助函数
function generateTableHTML(table: Table): string {
  const { headers, rows, title } = table
  
  let html = '<div class="table-container my-4">'
  
  if (title) {
    html += `<h3 class="text-lg font-semibold mb-2">${DOMPurify.sanitize(title)}</h3>`
  }
  
  html += '<div class="overflow-x-auto border rounded-lg">'
  html += '<table class="min-w-full bg-white">'
  
  // 表头
  if (headers && headers.length > 0) {
    html += '<thead class="bg-gray-50">'
    html += '<tr>'
    headers.forEach((header: string) => {
      html += `<th class="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">${DOMPurify.sanitize(header)}</th>`
    })
    html += '</tr>'
    html += '</thead>'
  }
  
  // 表体
  html += '<tbody>'
  if (rows && rows.length > 0) {
    rows.forEach((row: string[], index: number) => {
      const bgClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
      html += `<tr class="${bgClass}">`
      row.forEach((cell: string) => {
        html += `<td class="px-4 py-2 text-sm text-gray-900 border-b">${DOMPurify.sanitize(cell)}</td>`
      })
      html += '</tr>'
    })
  }
  html += '</tbody>'
  
  html += '</table>'
  html += '</div>'
  html += '</div>'
  
  return html
}