"use client"

import { useState, useEffect, useRef } from 'react'
import { Upload, FileText, Plus, Loader2, Check, X, ChevronDown, Trash2 } from 'lucide-react'
import { apiClient, DocumentTemplate, WordParseResult } from '@/lib/api-client'
import { toast } from 'sonner'

interface TemplateSelectorProps {
  onTemplateApplied: (result: WordParseResult) => void
  onTemplateSelected: (template: DocumentTemplate | null) => void
}

export function TemplateSelector({ onTemplateApplied, onTemplateSelected }: TemplateSelectorProps) {
  const [templates, setTemplates] = useState<DocumentTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [uploadMessage, setUploadMessage] = useState('')
  const [deletingId, setDeletingId] = useState<number | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      const templates = await apiClient.getTemplates()
      setTemplates(templates)
    } catch (error) {
      console.error('加载模板失败:', error)
    }
  }

  const handleTemplateSelect = (template: DocumentTemplate | null) => {
    setSelectedTemplate(template)
    onTemplateSelected(template)
    setShowDropdown(false)
  }

  const handleUploadTemplate = async (file: File) => {
    if (!file.name.endsWith('.docx')) {
      setUploadMessage('请选择 .docx 格式文件')
      return
    }

    setIsUploading(true)
    setUploadMessage('')

    try {
      // 处理 Word 文档
      const result = await apiClient.uploadAndProcessDocument(file)
      
      if (result.success) {
        setUploadMessage('文档处理成功!')
        onTemplateApplied(result)
        
        // 重新加载模板列表，获取最新上传的文档
        await loadTemplates()
      } else {
        setUploadMessage(`处理失败: ${result.errors?.join(', ') || '未知错误'}`)
      }
    } catch (error) {
      console.error('上传处理失败:', error)
      setUploadMessage(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsUploading(false)
      setTimeout(() => setUploadMessage(''), 3000)
    }
  }

  const triggerUpload = () => {
    fileInputRef.current?.click()
  }

  const handleDeleteTemplate = async (e: React.MouseEvent, templateId: number) => {
    e.stopPropagation() // 防止触发选择事件
    
    // 确认删除
    if (!confirm('确定要删除这个模板吗？此操作不可恢复。')) {
      return
    }

    setDeletingId(templateId)
    try {
      const result = await apiClient.deleteDocument(templateId)
      if (result.success) {
        toast.success('模板删除成功')
        // 如果删除的是当前选中的模板，清空选择
        if (selectedTemplate?.id === templateId) {
          setSelectedTemplate(null)
          onTemplateSelected(null)
        }
        // 重新加载模板列表
        await loadTemplates()
      } else {
        toast.error('删除失败')
      }
    } catch (error) {
      console.error('删除模板失败:', error)
      toast.error('删除模板失败，请稍后重试')
    } finally {
      setDeletingId(null)
    }
  }


  return (
    <div className="p-6 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-950/50">
      <div className="flex items-center mb-5">
        <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 mr-3">
          <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        </div>
        <h3 className="text-base font-bold text-gray-800 dark:text-gray-200 tracking-tight">
          报告模板
        </h3>
      </div>

      <div className="space-y-4">
        {/* 模板选择下拉框 */}
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={isLoading}
            className="w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md dark:hover:shadow-blue-900/20 transition-all duration-200 disabled:opacity-50 group"
          >
            <div className="flex items-center min-w-0 flex-1">
              <div className="flex items-center justify-center w-6 h-6 rounded-md bg-gray-100 dark:bg-gray-700 mr-3 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                <FileText className="h-3 w-3 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 truncate">
                {selectedTemplate ? selectedTemplate.filename.replace('.docx', '') : '选择模板'}
              </span>
            </div>
            <ChevronDown className={`h-4 w-4 text-gray-400 transition-all duration-200 group-hover:text-blue-500 ${showDropdown ? 'rotate-180' : ''}`} />
          </button>

          {showDropdown && (
            <div className="template-dropdown absolute z-10 w-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl dark:shadow-2xl backdrop-blur-sm animate-in slide-in-from-top-2 duration-200">
              <div className="py-2 max-h-60 overflow-y-auto custom-scrollbar">
                <button
                  onClick={() => handleTemplateSelect(null)}
                  className="w-full px-4 py-2.5 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700/50 flex items-center transition-colors"
                >
                  <div className="flex items-center justify-center w-5 h-5 rounded-md bg-gray-100 dark:bg-gray-700 mr-3">
                    <X className="h-3 w-3 text-gray-400" />
                  </div>
                  <span className="text-gray-500 dark:text-gray-400">不使用模板</span>
                  {!selectedTemplate && <Check className="h-4 w-4 ml-auto text-blue-500" />}
                </button>
                
                <div className="my-1 mx-3 border-t border-gray-100 dark:border-gray-700"></div>
                
                {templates.map((template) => (
                  <div
                    key={template.id}
                    className="group w-full px-4 py-2.5 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700/50 flex items-center justify-between transition-colors"
                  >
                    <button
                      onClick={() => handleTemplateSelect(template)}
                      className="flex-1 flex items-center justify-between min-w-0"
                    >
                      <div className="flex items-center min-w-0 flex-1">
                        <div className="flex items-center justify-center w-5 h-5 rounded-md bg-blue-50 dark:bg-blue-900/30 mr-3 flex-shrink-0">
                          <FileText className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 dark:text-gray-100 truncate text-sm">
                            {template.filename.replace('.docx', '')}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                            {new Date(template.created_at).toLocaleDateString('zh-CN', { 
                              year: 'numeric', 
                              month: 'short', 
                              day: 'numeric' 
                            })}
                          </div>
                        </div>
                      </div>
                      {selectedTemplate?.id === template.id && (
                        <div className="flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900/50 ml-3 flex-shrink-0">
                          <Check className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        </div>
                      )}
                    </button>
                    <button
                      onClick={(e) => handleDeleteTemplate(e, template.id)}
                      disabled={deletingId === template.id}
                      className="opacity-0 group-hover:opacity-100 p-1.5 ml-2 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200 disabled:opacity-50 flex-shrink-0"
                      title="删除模板"
                    >
                      {deletingId === template.id ? (
                        <Loader2 className="h-3.5 w-3.5 text-red-500 animate-spin" />
                      ) : (
                        <Trash2 className="h-3.5 w-3.5 text-red-500 hover:text-red-600 transition-colors" />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 上传按钮 */}
        <button
          onClick={triggerUpload}
          disabled={isUploading}
          className="w-full flex items-center justify-center px-4 py-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50/50 dark:hover:bg-blue-900/10 transition-all duration-200 disabled:opacity-50 group"
        >
          {isUploading ? (
            <>
              <div className="flex items-center justify-center w-6 h-6 rounded-md bg-blue-100 dark:bg-blue-900/30 mr-3">
                <Loader2 className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400 animate-spin" />
              </div>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">处理中...</span>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center w-6 h-6 rounded-md bg-gray-100 dark:bg-gray-700 mr-3 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                <Plus className="h-3.5 w-3.5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
              </div>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">上传新模板</span>
            </>
          )}
        </button>

        <input
          ref={fileInputRef}
          type="file"
          accept=".docx"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) {
              handleUploadTemplate(file)
            }
          }}
          className="hidden"
        />

        {/* 状态消息 */}
        {uploadMessage && (
          <div className={`p-3 rounded-xl text-sm flex items-center shadow-sm border animate-in slide-in-from-top-2 duration-300 ${
            uploadMessage.includes('成功') 
              ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-400 border-emerald-200 dark:border-emerald-800'
              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800'
          }`}>
            <div className={`flex items-center justify-center w-5 h-5 rounded-md mr-2.5 flex-shrink-0 ${
              uploadMessage.includes('成功') 
                ? 'bg-emerald-100 dark:bg-emerald-900/50' 
                : 'bg-red-100 dark:bg-red-900/50'
            }`}>
              {uploadMessage.includes('成功') ? (
                <Check className="h-3 w-3" />
              ) : (
                <X className="h-3 w-3" />
              )}
            </div>
            <span className="font-medium">{uploadMessage}</span>
          </div>
        )}

        {/* 选中模板信息 */}
        {selectedTemplate && (
          <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm">
            <div className="flex items-center text-sm text-blue-700 dark:text-blue-300">
              <div className="flex items-center justify-center w-6 h-6 rounded-md bg-blue-100 dark:bg-blue-900/50 mr-3">
                <FileText className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <span className="font-semibold truncate block">{selectedTemplate.filename.replace('.docx', '')}</span>
                <div className="text-xs text-blue-600/80 dark:text-blue-400/80 mt-0.5">
                  已选择 • {new Date(selectedTemplate.created_at).toLocaleDateString('zh-CN', { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}