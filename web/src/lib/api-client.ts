import { Chart, DocumentTemplate, WordParseResult } from '../types/chart'
import { chartGenerator } from './chart-generator'

// Re-export types for backward compatibility
export type { Chart, DocumentTemplate, WordParseResult } from '../types/chart'

const API_BASE_URL = process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000'

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  // 基础请求方法
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const defaultOptions: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(typeof window !== 'undefined' && localStorage.getItem('access_token')
          ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
          : {}),
      },
    }
    
    const config = { ...defaultOptions, ...options }
    
    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // 文件上传方法
  private async upload<T>(endpoint: string, file: File): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          ...(typeof window !== 'undefined' && localStorage.getItem('access_token')
            ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
            : {}),
        },
      })

      if (!response.ok) {
        throw new Error(`Upload failed! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('File upload failed:', error)
      throw error
    }
  }

  // 上传并处理Word文档
  async uploadAndProcessDocument(file: File): Promise<WordParseResult> {
    return this.upload<WordParseResult>('/api/word-format/upload-d3', file)
  }

  // 将Chart JSON配置转换为SVG字符串
  async generateChartSVG(chartConfig: Chart): Promise<string> {
    return chartGenerator.generateChartSVG(chartConfig)
  }

  // 获取所有文档模板
  async getDocuments(): Promise<DocumentTemplate[]> {
    return this.request<DocumentTemplate[]>('/api/word-format/documents')
  }

  // 获取文档详细内容
  async getDocumentContent(docId: number): Promise<DocumentTemplate> {
    return this.request<DocumentTemplate>(`/api/word-format/documents/${docId}`)
  }

  // 删除文档模板
  async deleteDocumentTemplate(templateId: number): Promise<{ success: boolean; message?: string }> {
    return this.request(`/api/word-format/documents/${templateId}`, {
      method: 'DELETE'
    })
  }

  // 导出Word文档
  async exportWord(markdown: string): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/api/word-format/export-word`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ markdown })
    })

    if (!response.ok) {
      throw new Error(`Export failed! status: ${response.status}`)
    }

    return response.blob()
  }

  // 导出PDF文档  
  async exportPdf(markdown: string): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/api/word-format/export-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ markdown })
    })

    if (!response.ok) {
      throw new Error(`PDF export failed! status: ${response.status}`)
    }

    return response.blob()
  }

  // 删除文档（删除接口）
  async deleteDocument(docId: number): Promise<{ success: boolean; message?: string }> {
    return this.request(`/api/word-format/documents/${docId}`, {
      method: 'DELETE'
    })
  }

  // 获取模板列表 (别名方法，保持向后兼容)
  async getTemplates(): Promise<DocumentTemplate[]> {
    return this.getDocuments()
  }
}

export const apiClient = new ApiClient()