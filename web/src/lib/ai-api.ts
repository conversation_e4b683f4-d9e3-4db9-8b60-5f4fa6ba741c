/**
 * AI API 集成模块
 * 负责与后端AI服务的通信
 */

import { getAIConfig, formatBackendUrl, validateAIConfig } from '@/config/ai-config'

// AI API 配置
export interface AIConfig {
  baseUrl: string
  apiKey?: string
  timeout?: number
}

// AI 请求选项 - 简化版
export interface AIRequestOptions {
  prompt: string
  context?: string
  selected_text?: string
}

// AI 响应数据
export interface AIResponseChunk {
  data: string
  done?: boolean
}

// 获取默认配置
function getDefaultConfig(): AIConfig {
  const envConfig = getAIConfig()
  return {
    baseUrl: envConfig.backendUrl,
    timeout: envConfig.timeout,
  }
}

let currentConfig: AIConfig = getDefaultConfig()

/**
 * 配置AI API
 */
export function configureAI(config: Partial<AIConfig>) {
  const newConfig = { ...currentConfig, ...config }

  // 验证配置
  const envConfig = getAIConfig()
  if (!validateAIConfig({
    ...envConfig,
    backendUrl: newConfig.baseUrl,
    timeout: newConfig.timeout || envConfig.timeout,
  })) {
    console.warn('AI配置验证失败，使用默认配置')
    return
  }

  currentConfig = newConfig
}

/**
 * 获取当前AI配置
 */
export function getCurrentAIConfig(): AIConfig {
  return { ...currentConfig }
}

/**
 * 解析服务URL
 */
function resolveServiceURL(path: string): string {
  return formatBackendUrl(currentConfig.baseUrl, path)
}

/**
 * 流式获取AI响应 - 增强版本，支持实时流式处理
 */
export async function* fetchAIStream(
  options: AIRequestOptions
): AsyncIterable<AIResponseChunk> {
  // 在开发环境中，优先使用代理路径避免CORS问题
  const isDevelopment = process.env.NODE_ENV === 'development'
  const url = isDevelopment
    ? '/api/prose/generate' // 使用Next.js代理
    : resolveServiceURL('/api/prose/generate') // 直接调用后端

  // 构建请求头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
  }

  // 添加认证头
  if (currentConfig.apiKey) {
    headers['Authorization'] = `Bearer ${currentConfig.apiKey}`
  }

  // 构建最简化的请求体，避免参数重复
  const requestBody = {
    prompt: options.prompt,
    context: options.context || '',
    selected_text: options.selected_text || '',
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      mode: 'cors',
      credentials: 'omit',
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(currentConfig.timeout || 60000), // 增加超时时间
    })

    if (!response.ok) {
      // 提供更详细的错误信息
      let errorMessage = `AI API request failed: ${response.status} ${response.statusText}`

      try {
        const errorText = await response.text()
        if (errorText) {
          const errorData = JSON.parse(errorText)
          errorMessage += ` - ${errorData.error || errorData.detail || errorText}`
        }
      } catch (e) {
        // 忽略解析错误响应的错误
      }

      throw new Error(errorMessage)
    }

    if (!response.body) {
      throw new Error('Response body is empty')
    }

    // 解析流式响应
    yield* parseStreamResponse(response.body)

  } catch (error) {

    // 处理不同类型的错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error(`Network connection failed. Please check if backend service is running at ${currentConfig.baseUrl}`)
    }

    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out. The AI service may be overloaded.')
    }

    if (error instanceof Error && error.message.includes('CORS')) {
      throw new Error(`CORS error. Please check backend service CORS configuration.`)
    }

    throw error
  }
}

/**
 * 解析流式响应 - 增强版本，更好地处理实时流
 */
async function* parseStreamResponse(
  body: ReadableStream<Uint8Array>
): AsyncIterable<AIResponseChunk> {
  const reader = body.pipeThrough(new TextDecoderStream() as any).getReader()
  let buffer = ''
  let isComplete = false

  try {
    while (true) {
      const { done, value } = await reader.read()

      if (done) {
        // 处理剩余的buffer内容
        if (buffer.trim()) {
          const remainingEvents = buffer.split('\n\n').filter(chunk => chunk.trim())
          for (const chunk of remainingEvents) {
            const event = parseEvent(chunk)
            if (event && event.data) {
              yield { data: event.data, done: false }
            }
          }
        }
        
        // 发送完成信号
        if (!isComplete) {
          yield { data: '', done: true }
        }
        break
      }

      buffer += value

      // 按照Server-Sent Events格式解析，事件以'\n\n'结束
      while (true) {
        const index = buffer.indexOf('\n\n')
        if (index === -1) {
          break
        }

        const chunk = buffer.slice(0, index)
        buffer = buffer.slice(index + 2)

        const event = parseEvent(chunk)
        if (event) {
          if (event.event === 'complete') {
            isComplete = true
            yield { data: '', done: true }
          } else if (event.event === 'error') {
            throw new Error(`AI Stream Error: ${event.data}`)
          } else if (event.data) {
            yield { data: event.data, done: false }
          }
        }
      }
    }
  } catch (error) {
    throw error
  } finally {
    reader.releaseLock()
  }
}

/**
 * 解析单个事件 - 增强版本，支持更多事件类型
 */
function parseEvent(chunk: string): { event: string; data: string } | null {
  let resultEvent = 'message'
  let resultData: string | null = null

  // 处理空行或无效数据
  if (!chunk.trim()) {
    return null
  }

  for (const line of chunk.split('\n')) {
    const pos = line.indexOf(': ')
    if (pos === -1) {
      // 如果没有找到分隔符，可能是纯文本数据
      if (line.trim() && !resultData) {
        resultData = line.trim()
      }
      continue
    }

    const key = line.slice(0, pos).trim()
    const value = line.slice(pos + 2)

    if (key === 'event') {
      resultEvent = value
    } else if (key === 'data') {
      resultData = value
    }
  }

  // 如果是message事件但没有数据，返回null
  if (resultEvent === 'message' && (resultData === null || resultData === '')) {
    return null
  }

  return {
    event: resultEvent,
    data: resultData || '',
  }
}

/**
 * 非流式获取AI响应
 */
export async function fetchAICompletion(
  options: AIRequestOptions
): Promise<string> {
  let fullResponse = ''
  
  for await (const chunk of fetchAIStream(options)) {
    fullResponse += chunk.data
  }
  
  return fullResponse
}
