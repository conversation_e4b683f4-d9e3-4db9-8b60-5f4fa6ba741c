import { Chart, ChartDataset } from '../types/chart'

/**
 * ChartGenerator - 专门负责生成SVG图表的类
 * 遵循单一职责原则，只负责图表渲染逻辑
 */
export class ChartGenerator {
  private readonly colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316', '#84CC16']
  
  /**
   * 根据图表配置生成SVG字符串
   */
  generateChartSVG(chartConfig: Chart): string {
    const { type, title, data, datasets } = chartConfig
    
    // Handle nested config structure
    const config = (chartConfig as any).config || chartConfig
    const chartType = config.type || type
    const chartTitle = config.title || title || '图表'
    const chartData = config.data || data
    const chartDatasets = config.datasets || datasets
    const chartCategories = config.categories || chartConfig.categories

    console.log('Chart config:', { chartType, chartTitle, chartData, chartDatasets, chartCategories })
    
    switch (chartType) {
      case 'simple-pie':
        return chartDatasets ? this._generatePieSVG(chartDatasets, chartTitle) : this._generateErrorSVG('No pie chart data', chartTitle)
      
      case 'nested-pie':
        return chartData ? this._generateNestedPieSVG(chartData, chartTitle) : this._generateErrorSVG('No nested pie data', chartTitle)
      
      case 'combo':
        return chartDatasets ? this._generateComboSVG(chartDatasets, chartCategories, chartTitle) : this._generateErrorSVG('No combo data', chartTitle)
      
      case 'error':
        return this._generateErrorSVG(chartConfig.error, chartTitle)
      
      default:
        console.warn('Unsupported chart type:', chartType, 'Config:', chartConfig)
        return this._generateErrorSVG(`Unsupported chart type: ${chartType}`, chartTitle)
    }
  }

  private _generatePieSVG(datasets: ChartDataset[], title?: string): string {
    const width = 480
    const height = 400
    const radius = 90
    const centerX = width / 2
    const centerY = height / 2 - 30
    
    if (!datasets || datasets.length === 0) {
      return this._generateErrorSVG('No pie chart data available', title)
    }

    const total = datasets.reduce((sum, d) => sum + (d.value || 0), 0)
    let currentAngle = -Math.PI / 2 // Start from top

    let paths = ''
    let legends = ''
    
    datasets.forEach((d, i) => {
      const value = d.value || 0
      const angle = (value / total) * 2 * Math.PI
      const startAngle = currentAngle
      const endAngle = currentAngle + angle
      
      const x1 = centerX + radius * Math.cos(startAngle)
      const y1 = centerY + radius * Math.sin(startAngle)
      const x2 = centerX + radius * Math.cos(endAngle)
      const y2 = centerY + radius * Math.sin(endAngle)
      
      const largeArcFlag = angle > Math.PI ? 1 : 0
      const color = this.colors[i % this.colors.length]
      
      // Create path with modern styling
      paths += `
        <path d="M ${centerX} ${centerY} 
                 L ${x1} ${y1} 
                 A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} 
                 Z" 
              fill="${color}" 
              stroke="#FFFFFF" 
              stroke-width="2"
              filter="url(#shadow)"
              class="hover:opacity-80 transition-opacity cursor-pointer"/>
      `
      
      // Add percentage label
      const labelAngle = startAngle + angle / 2
      const labelX = centerX + (radius * 0.75) * Math.cos(labelAngle)
      const labelY = centerY + (radius * 0.75) * Math.sin(labelAngle)
      const percentage = ((value / total) * 100).toFixed(1)
      
      if (Number(percentage) > 5) { // Only show label if slice is large enough
        paths += `<text x="${labelX}" y="${labelY}" text-anchor="middle" font-size="12" font-family="system-ui, -apple-system, sans-serif" fill="white" font-weight="600">${percentage}%</text>`
      }
      
      currentAngle += angle
    })
    
    // Create legend
    datasets.forEach((d, i) => {
      const legendY = height - 80 + (i % 2) * 25
      const legendX = 40 + Math.floor(i / 2) * 160
      legends += `
        <rect x="${legendX}" y="${legendY - 8}" width="14" height="14" fill="${this.colors[i % this.colors.length]}" rx="2"/>
        <text x="${legendX + 20}" y="${legendY + 3}" font-size="12" font-family="system-ui, -apple-system, sans-serif" fill="#374151">${d.name}</text>
      `
    })

    return `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="4" flood-color="#00000020"/>
          </filter>
        </defs>
        
        <rect width="${width}" height="${height}" fill="#FAFAFA" rx="8"/>
        <text x="${centerX}" y="30" text-anchor="middle" font-size="18" font-weight="600" font-family="system-ui, -apple-system, sans-serif" fill="#111827">${title || '饼图'}</text>
        
        <g>${paths}</g>
        <g>${legends}</g>
      </svg>
    `
  }

  private _generateNestedPieSVG(data: any, title?: string): string {
    const width = 500
    const height = 420
    const centerX = width / 2
    const centerY = height / 2 - 20
    const outerRadius = 110
    const innerRadius = 70
    
    if (!data || !data.outer || !data.inner) {
      return this._generateErrorSVG('Nested pie data incomplete', title)
    }

    let outerPaths = ''
    let innerPaths = ''
    let currentAngle = -Math.PI / 2

    // Generate outer ring
    data.outer.forEach((segment: any, i: number) => {
      const angle = segment.value * 2 * Math.PI
      const startAngle = currentAngle
      const endAngle = currentAngle + angle

      const x1 = centerX + outerRadius * Math.cos(startAngle)
      const y1 = centerY + outerRadius * Math.sin(startAngle)
      const x2 = centerX + outerRadius * Math.cos(endAngle)
      const y2 = centerY + outerRadius * Math.sin(endAngle)
      const ix1 = centerX + innerRadius * Math.cos(startAngle)
      const iy1 = centerY + innerRadius * Math.sin(startAngle)
      const ix2 = centerX + innerRadius * Math.cos(endAngle)
      const iy2 = centerY + innerRadius * Math.sin(endAngle)

      const largeArcFlag = angle > Math.PI ? 1 : 0
      const color = this.colors[i % this.colors.length]

      outerPaths += `
        <path d="M ${ix1} ${iy1} 
                 L ${x1} ${y1} 
                 A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}
                 L ${ix2} ${iy2}
                 A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${ix1} ${iy1}
                 Z" 
              fill="${color}" 
              stroke="white" 
              stroke-width="2"
              filter="url(#shadow)"/>
      `

      // Add percentage label for outer ring
      const labelRadius = (outerRadius + innerRadius) / 2
      const labelAngle = startAngle + angle / 2
      const labelX = centerX + labelRadius * Math.cos(labelAngle)
      const labelY = centerY + labelRadius * Math.sin(labelAngle)
      const percentage = ((segment.value) * 100).toFixed(1)
      
      if (Number(percentage) > 8) { // Only show label if slice is large enough
        outerPaths += `<text x="${labelX}" y="${labelY}" text-anchor="middle" font-size="11" font-family="system-ui, -apple-system, sans-serif" fill="white" font-weight="600">${percentage}%</text>`
      }
      
      currentAngle += angle
    })

    // Generate inner circle
    currentAngle = -Math.PI / 2
    data.inner.forEach((segment: any, i: number) => {
      const angle = segment.value * 2 * Math.PI
      const startAngle = currentAngle
      const endAngle = currentAngle + angle

      const x1 = centerX + innerRadius * Math.cos(startAngle)
      const y1 = centerY + innerRadius * Math.sin(startAngle)
      const x2 = centerX + innerRadius * Math.cos(endAngle)
      const y2 = centerY + innerRadius * Math.sin(endAngle)

      const largeArcFlag = angle > Math.PI ? 1 : 0
      const color = this.colors[(i + 4) % this.colors.length] // Offset colors

      innerPaths += `
        <path d="M ${centerX} ${centerY} 
                 L ${x1} ${y1} 
                 A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} 
                 Z" 
              fill="${color}" 
              stroke="white" 
              stroke-width="2"
              filter="url(#shadow)"/>
      `
      
      currentAngle += angle
    })

    // Generate legends
    let legends = ''
    data.outer.forEach((segment: any, i: number) => {
      const legendY = height - 60 + (i % 2) * 20
      const legendX = 50 + Math.floor(i / 2) * 200
      legends += `
        <rect x="${legendX}" y="${legendY - 6}" width="12" height="12" fill="${this.colors[i % this.colors.length]}" rx="2"/>
        <text x="${legendX + 18}" y="${legendY + 3}" font-size="11" font-family="system-ui, -apple-system, sans-serif" fill="#374151">${segment.name}</text>
      `
    })

    return `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000020"/>
          </filter>
        </defs>
        
        <rect width="${width}" height="${height}" fill="#FAFAFA" rx="8"/>
        <text x="${centerX}" y="30" text-anchor="middle" font-size="18" font-weight="600" font-family="system-ui, -apple-system, sans-serif" fill="#111827">${title || '嵌套饼图'}</text>
        
        <g>${outerPaths}${innerPaths}</g>
        <g>${legends}</g>
      </svg>
    `
  }

  private _generateComboSVG(datasets: ChartDataset[], _categories: string[] = [], title?: string): string {
    const width = 650
    const height = 450
    const margin = { top: 70, right: 100, bottom: 90, left: 90 }
    const chartWidth = width - margin.left - margin.right
    const chartHeight = height - margin.top - margin.bottom
    
    if (!datasets || datasets.length === 0) {
      return this._generateErrorSVG('No data available', title)
    }

    const barDataset = datasets.find(d => d.type === 'bar')
    const lineDataset = datasets.find(d => d.type === 'line')
    
    if (!barDataset || !lineDataset) {
      return this._generateErrorSVG('Combo chart requires both bar and line data', title)
    }

    const barData = barDataset.data || []
    const lineData = lineDataset.data || []
    const maxBarValue = Math.max(...barData, 1)
    const minBarValue = Math.min(...barData, 0)
    const maxLineValue = Math.max(...lineData, 1) 
    const minLineValue = Math.min(...lineData, 0)

    const colors = { bar: '#3B82F6', line: '#EF4444' }
    let bars = ''
    let linePoints = ''
    let gridLines = ''
    let yAxisLabels = ''

    // Generate grid lines and y-axis labels
    for (let i = 0; i <= 5; i++) {
      const y = margin.top + (i * chartHeight / 5)
      const value = maxBarValue - (i * (maxBarValue - minBarValue) / 5)
      gridLines += `<line x1="${margin.left}" y1="${y}" x2="${margin.left + chartWidth}" y2="${y}" stroke="#E5E7EB" stroke-width="1"/>`
      yAxisLabels += `<text x="${margin.left - 10}" y="${y + 5}" text-anchor="end" font-size="10" font-family="system-ui, -apple-system, sans-serif" fill="#6B7280">${Math.round(value)}</text>`
    }

    // Generate bars
    const barWidth = chartWidth / Math.max(barData.length, 1)
    barData.forEach((value, i) => {
      const barHeight = ((value - minBarValue) / (maxBarValue - minBarValue)) * chartHeight
      const x = margin.left + i * barWidth + barWidth * 0.2
      const y = margin.top + chartHeight - barHeight
      const actualBarWidth = barWidth * 0.6

      bars += `<rect x="${x}" y="${y}" width="${actualBarWidth}" height="${barHeight}" fill="${colors.bar}" stroke="white" stroke-width="1" rx="2" filter="url(#shadow)"/>`
    })

    // Generate line
    const linePointsArray: string[] = []
    lineData.forEach((value, i) => {
      const x = margin.left + i * barWidth + barWidth * 0.5
      const y = margin.top + chartHeight - ((value - minLineValue) / (maxLineValue - minLineValue)) * chartHeight
      linePointsArray.push(`${x},${y}`)
      linePoints += `<circle cx="${x}" cy="${y}" r="4" fill="${colors.line}" stroke="white" stroke-width="2" filter="url(#shadow)"/>`
    })

    const linePath = `<polyline points="${linePointsArray.join(' ')}" fill="none" stroke="${colors.line}" stroke-width="3" stroke-linejoin="round" filter="url(#shadow)"/>`

    return `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="1" dy="1" stdDeviation="2" flood-color="#00000020"/>
          </filter>
        </defs>
        
        <rect width="${width}" height="${height}" fill="#FAFAFA" rx="8"/>
        <rect x="${margin.left}" y="${margin.top}" width="${chartWidth}" height="${chartHeight}" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1" rx="4"/>
        
        ${gridLines}
        
        <text x="${width/2}" y="30" text-anchor="middle" font-size="18" font-weight="600" font-family="system-ui, -apple-system, sans-serif" fill="#111827">${title || '组合图表'}</text>
        
        <!-- Axes -->
        <line x1="${margin.left}" y1="${margin.top}" x2="${margin.left}" y2="${margin.top + chartHeight}" stroke="#374151" stroke-width="2"/>
        <line x1="${margin.left + chartWidth}" y1="${margin.top}" x2="${margin.left + chartWidth}" y2="${margin.top + chartHeight}" stroke="#374151" stroke-width="2"/>
        <line x1="${margin.left}" y1="${margin.top + chartHeight}" x2="${margin.left + chartWidth}" y2="${margin.top + chartHeight}" stroke="#374151" stroke-width="2"/>
        
        ${yAxisLabels}
        
        ${bars}
        ${linePath}
        ${linePoints}
        
        <!-- Legend -->
        <g transform="translate(${width - 90}, 60)">
          <rect x="0" y="0" width="14" height="14" fill="${colors.bar}" rx="2"/>
          <text x="20" y="12" font-size="12" font-family="system-ui, -apple-system, sans-serif" fill="#374151">${barDataset.name || '柱状数据'}</text>
          <line x1="0" y1="30" x2="14" y2="30" stroke="${colors.line}" stroke-width="3"/>
          <circle cx="7" cy="30" r="3" fill="${colors.line}"/>
          <text x="20" y="34" font-size="12" font-family="system-ui, -apple-system, sans-serif" fill="#374151">${lineDataset.name || '线性数据'}</text>
        </g>
      </svg>
    `
  }

  private _generateErrorSVG(error?: string, title?: string): string {
    return `
      <svg width="400" height="250" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="errorGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#FEF2F2;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#FECACA;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <rect width="400" height="250" fill="url(#errorGradient)" stroke="#F87171" stroke-width="2" rx="8"/>
        
        <circle cx="200" cy="80" r="25" fill="#EF4444" opacity="0.1"/>
        <path d="M200 65 L200 85 M200 95 L200 95" stroke="#EF4444" stroke-width="3" stroke-linecap="round"/>
        
        <text x="200" y="130" text-anchor="middle" font-size="16" font-weight="600" font-family="system-ui, -apple-system, sans-serif" fill="#7F1D1D">${title || '图表加载失败'}</text>
        <text x="200" y="160" text-anchor="middle" font-size="13" font-family="system-ui, -apple-system, sans-serif" fill="#991B1B">错误: ${error || '未知错误'}</text>
        <text x="200" y="190" text-anchor="middle" font-size="11" font-family="system-ui, -apple-system, sans-serif" fill="#7F1D1D">请检查数据格式或稍后重试</text>
      </svg>
    `
  }
}

export const chartGenerator = new ChartGenerator()