"use client"

import { useState, useRef, useCallback } from 'react'
import {
  PanelGroup as ResizablePanelGroup,
  Panel as ResizablePanel,
  PanelResizeHandle as Resizable<PERSON>andle
} from 'react-resizable-panels';
import { AIEditor, AIEditorHandle } from '@/components/ai-editor'
import { TemplateSelector } from '@/components/template-selector'
import { ReportOutline } from '@/components/report-outline'
import { AIPanel } from '@/components/ai-panel'
import { WordParseResult, DocumentTemplate, apiClient } from '@/lib/api-client'
import { ChartRenderer, TableRenderer } from '@/components/chart-renderer'
import { Save, Download, FileText, Sparkles } from 'lucide-react'
import { toast } from 'sonner'

const downloadFile = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};



export default function Home() {
  const [markdown, setMarkdown] = useState<string>('# 🎉 欢迎使用智能AI报告编辑器\n\n开始编辑您的报告内容...')
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null)
  const [selectedText, setSelectedText] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const editorRef = useRef<AIEditorHandle>(null)

  const [currentDocument, setCurrentDocument] = useState<DocumentTemplate | null>(null)

  const handleTemplateApplied = useCallback((result: WordParseResult) => {
    if (result.success && result.markdown_content) {
      // 设置markdown内容（包含图表和表格占位符）
      editorRef.current?.setMarkdown(result.markdown_content);
      setMarkdown(result.markdown_content);
      
      // 保存完整文档数据供左侧面板使用
      setCurrentDocument({
        id: Date.now(),
        filename: selectedTemplate?.filename || 'document.docx',
        markdown_content: result.markdown_content,
        tables: result.tables || [],
        charts: result.charts || [],
        created_at: new Date().toISOString()
      });
      
      toast.success(`成功加载文档！包含 ${result.charts?.length || 0} 个图表和 ${result.tables?.length || 0} 个表格`)
    }
  }, [selectedTemplate])

  const handleTemplateSelected = useCallback(async (template: DocumentTemplate | null) => {
    setSelectedTemplate(template)
    if (!template) return

    setIsLoading(true)
    try {
      // 拉取模板完整内容
      const doc = await apiClient.getDocumentContent(template.id)

      // 将 Markdown 写入编辑器
      if (doc.markdown_content) {
        editorRef.current?.setMarkdown(doc.markdown_content)
        setMarkdown(doc.markdown_content)
      }

      // 同步左侧预览数据
      setCurrentDocument({
        id: doc.id,
        filename: doc.filename,
        markdown_content: doc.markdown_content || '',
        tables: doc.tables || [],
        charts: doc.charts || [],
        created_at: doc.created_at,
      })

      toast.success(`已加载模板：${doc.filename.replace('.docx', '')}`)
    } catch (error) {
      console.error('加载模板失败:', error)
      toast.error('加载模板失败，请稍后重试。')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const handleOutlineNavigate = useCallback((position: number) => {
    editorRef.current?.navigateTo(position);
  }, [])

  const handleInsertText = useCallback((text: string) => {
    editorRef.current?.insertText(text);
  }, [])

  const handleReplaceText = useCallback((text: string) => {
    editorRef.current?.replaceText(text);
  }, [])

  const handleSave = () => {
    const currentMarkdown = editorRef.current?.getMarkdown();
    if (currentMarkdown) {
      const blob = new Blob([currentMarkdown], { type: 'text/markdown;charset=utf-8' });
      const filename = `${selectedTemplate?.filename?.replace('.docx', '') || 'report'}-${new Date().toISOString().split('T')[0]}.md`;
      downloadFile(blob, filename);
      toast.success('报告已成功保存为 Markdown 文件。')
    } else {
      toast.info('没有内容可以保存。')
    }
  }

  const handleExportWord = async () => {
    const currentMarkdown = editorRef.current?.getMarkdown();
    if (!currentMarkdown) {
      toast.info('没有内容可以导出。');
      return;
    }

    setIsLoading(true);
    try {
      const blob = await apiClient.exportWord(currentMarkdown);
      const filename = `${selectedTemplate?.filename?.replace('.docx', '') || 'report'}.docx`;
      downloadFile(blob, filename);
      toast.success('报告已成功导出为 Word 文档。')
    } catch (error) {
      console.error("Failed to export Word:", error);
      toast.error('导出 Word 文档失败，请稍后重试。')
    } finally {
      setIsLoading(false);
    }
  }

  const handleExportPDF = async () => {
    const currentMarkdown = editorRef.current?.getMarkdown();
    if (!currentMarkdown) {
      toast.info('没有内容可以导出。')
      return;
    }
    
    setIsLoading(true);
    try {
      const blob = await apiClient.exportPdf(currentMarkdown);
      const filename = `${selectedTemplate?.filename?.replace('.docx', '') || 'report'}.pdf`;
      downloadFile(blob, filename);
      toast.success('报告已成功导出为 PDF 文档。')
    } catch (error) {
      console.error("Failed to export PDF:", error);
      toast.error('导出 PDF 文档失败，请稍后重试。')
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* 顶部工具栏 */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Sparkles className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">智能AI报告编辑器</h1>
            </div>
            
            {selectedTemplate && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <FileText className="h-4 w-4 mr-1" />
                <span>模板: {selectedTemplate.filename.replace('.docx', '')}</span>
              </div>
            )}
            
            {isLoading && (
              <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-1" />
                <span>处理中...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="toolbar-button flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-1" />
              <span>保存</span>
            </button>
            
            <div className="flex items-center space-x-1">
              <button
                onClick={handleExportWord}
                disabled={isLoading}
                className="toolbar-button flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="h-4 w-4 mr-1" />
                <span>Word</span>
              </button>
              
              <button
                onClick={handleExportPDF}
                disabled={isLoading}
                className="toolbar-button flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="h-4 w-4 mr-1" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 - 三栏布局 */}
      <main className="flex-1 overflow-hidden flex flex-col">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* 左侧栏 - 模板选择和大纲 */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
            <div className="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
              <TemplateSelector
                onTemplateApplied={handleTemplateApplied}
                onTemplateSelected={handleTemplateSelected}
              />
              
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {/* 图表和表格展示区域 */}
                {currentDocument && ((currentDocument.charts?.length ?? 0) > 0 || (currentDocument.tables?.length ?? 0) > 0) && (
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">文档内容</h3>
                    
                    {/* 图表展示 */}
                    {(currentDocument.charts?.length ?? 0) > 0 && (
                      <div className="mb-4">
                        <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                          图表 ({currentDocument.charts?.length ?? 0})
                        </h4>
                        <div className="space-y-2">
                          {currentDocument.charts?.map((chart: any, index: number) => (
                            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
                              <div className="text-xs text-gray-600 dark:text-gray-300 mb-1">
                                {chart.title || `图表 ${index + 1}`}
                              </div>
                              <ChartRenderer 
                                chartConfig={chart} 
                                className="w-full max-w-xs"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {/* 表格展示 */}
                    {(currentDocument.tables?.length ?? 0) > 0 && (
                      <div className="mb-4">
                        <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                          表格 ({currentDocument.tables?.length ?? 0})
                        </h4>
                        <div className="space-y-2">
                          {currentDocument.tables?.map((table: any, index: number) => (
                            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
                              <div className="text-xs text-gray-600 dark:text-gray-300 mb-1">
                                {table.title || `表格 ${index + 1}`}
                              </div>
                              <TableRenderer 
                                tableData={table} 
                                className="text-xs"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                <ReportOutline
                  markdownContent={markdown}
                  onNavigate={handleOutlineNavigate}
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-300 dark:hover:bg-blue-600 transition-colors" />

          {/* 中间编辑区域 */}
          <ResizablePanel defaultSize={55} minSize={40}>
            <div className="h-full bg-white dark:bg-gray-800 flex flex-col">
             
              
              <div className="flex-1 overflow-hidden">
                <AIEditor
                  ref={editorRef}
                  initialContent={{
                    type: "doc",
                    content: [
                      {
                        type: "heading",
                        attrs: { level: 1 },
                        content: [{ type: "text", text: "🎉 欢迎使用智能AI报告编辑器" }]
                      },
                      {
                        type: "paragraph",
                        content: [{ type: "text", text: "开始编辑您的报告内容..." }]
                      }
                    ]
                  }}
                  placeholder="开始写作，体验AI功能..."
                  onMarkdownChange={setMarkdown}
                  onSelectionChange={setSelectedText}
                  showToolbar={true}
                  defaultMode="edit"
                  className="h-full"
                  aiConfig={{
                    baseUrl: process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000',
                    timeout: 300000,
                  }}
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-300 dark:hover:bg-blue-600 transition-colors" />

          {/* 右侧AI助手面板 */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
            <div className="h-full border-l border-gray-200 dark:border-gray-700">
              <AIPanel
                selectedText={selectedText}
                fullText={markdown}
                onInsert={handleInsertText}
                onReplace={handleReplaceText}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </main>
    </div>
  )
}
