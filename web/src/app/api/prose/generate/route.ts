/**
 * Next.js API Route for AI Streaming Proxy
 * Proxies requests to the backend AI service to avoid CORS issues in development
 */

import { NextRequest } from 'next/server'

const BACKEND_URL = process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000'

export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json()
    
    // Validate required fields
    if (!body.prompt || typeof body.prompt !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Prompt is required and must be a string' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Forward request to backend
    const backendResponse = await fetch(`${BACKEND_URL}/api/prose/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        // 透传用户端 Authorization（如果有）
        ...(request.headers.get('authorization') ? { Authorization: request.headers.get('authorization')! } : {}),
      },
      body: JSON.stringify({
        prompt: body.prompt,
        option: body.option || 'zap',
        command: body.command || '',
        context: body.context || '',
        selected_text: body.selected_text || '',
        template_type: body.template_type || 'general',
        current_section: body.current_section || '',
      }),
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      console.error('Backend error:', backendResponse.status, errorText)
      
      return new Response(
        JSON.stringify({ 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        }), 
        { 
          status: backendResponse.status,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Stream the response back to the client
    const stream = new ReadableStream({
      async start(controller) {
        const reader = backendResponse.body?.getReader()
        
        if (!reader) {
          controller.error(new Error('No response body'))
          return
        }

        try {
          const decoder = new TextDecoder()
          
          while (true) {
            const { done, value } = await reader.read()
            
            if (done) {
              controller.close()
              break
            }
            
            // Forward the chunk to the client
            const chunk = decoder.decode(value, { stream: true })
            controller.enqueue(new TextEncoder().encode(chunk))
          }
          
        } catch (error) {
          console.error('Stream error:', error)
          controller.error(error)
        } finally {
          reader.releaseLock()
        }
      },
    })

    // Return streaming response with proper headers
    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Transfer-Encoding': 'chunked',
      },
    })

  } catch (error) {
    console.error('Proxy error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}