from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from sqlalchemy.orm import Session

from database.connection import get_db
from services.auth_service import AuthService
from core.logging import get_logger
from core.rate_limit import limit_requests
from core.security import get_current_user
from config import get_settings

auth_router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
)

logger = get_logger(__name__)

@auth_router.post("/login", dependencies=[Depends(limit_requests(10, 60))])
async def login_for_next_auth(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], 
    db: Session = Depends(get_db),
    request: Request = None,
):
    user = AuthService.authenticate_user(
        db, email=form_data.username, password=form_data.password
    )
    
    if not user:
        logger.warning(f"用户认证失败: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    # 签发访问令牌
    settings = get_settings()
    access_token = AuthService.create_access_token(
        data={"sub": str(user.id), "role": user.role}
    )
    # 创建刷新会话
    session = AuthService.create_session(
        db,
        user,
        ip_address=request.client.host if request and request.client else None,
        user_agent=request.headers.get("User-Agent") if request else None,
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": session.session_token,
        "user": {"id": str(user.id), "name": user.full_name, "email": user.email, "role": user.role},
        "expires_in": settings.auth.access_token_expire_minutes * 60,
    }


@auth_router.post("/refresh", dependencies=[Depends(limit_requests(30, 60))])
async def refresh_access_token(
    refresh_token: str,
    db: Session = Depends(get_db),
    request: Request = None,
):
    from models.database import User, UserSession
    from datetime import datetime

    session = db.query(UserSession).filter(
        UserSession.session_token == refresh_token,
        UserSession.is_active == True,
    ).first()
    if not session or session.expires_at < datetime.utcnow():
        raise HTTPException(status_code=401, detail="Invalid or expired refresh token")

    user = db.query(User).filter_by(id=session.user_id).first()
    if not user or not user.is_active:
        raise HTTPException(status_code=401, detail="User not found or inactive")

    # 旋转刷新令牌
    session = AuthService.rotate_session(
        db, session,
        ip_address=request.client.host if request and request.client else None,
        user_agent=request.headers.get("User-Agent") if request else None,
    )

    access_token = AuthService.create_access_token(data={"sub": str(user.id), "role": user.role})
    settings = get_settings()
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": session.session_token,
        "expires_in": settings.auth.access_token_expire_minutes * 60,
    }


